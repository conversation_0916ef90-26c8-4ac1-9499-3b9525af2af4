#!/bin/bash

# Java应用部署和监控脚本
# 用于部署优化后的资源配置并监控效果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
APP_NAME="java-app"
NAMESPACE="default"
DEPLOYMENT_FILE="java-app-k8s-template.yaml"
MONITOR_DURATION=300  # 监控5分钟

echo -e "${BLUE}=== Java应用资源优化部署脚本 ===${NC}"
echo "应用名称: $APP_NAME"
echo "命名空间: $NAMESPACE"
echo "部署时间: $(date)"
echo

# 检查必要工具
check_prerequisites() {
    echo -e "${GREEN}检查前置条件...${NC}"
    
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}错误: kubectl未安装${NC}"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        echo -e "${RED}错误: 无法连接到Kubernetes集群${NC}"
        exit 1
    fi
    
    if [ ! -f "$DEPLOYMENT_FILE" ]; then
        echo -e "${RED}错误: 部署文件 $DEPLOYMENT_FILE 不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 前置条件检查通过${NC}"
}

# 备份当前配置
backup_current_config() {
    echo -e "${GREEN}备份当前配置...${NC}"
    
    if kubectl get deployment $APP_NAME -n $NAMESPACE &> /dev/null; then
        kubectl get deployment $APP_NAME -n $NAMESPACE -o yaml > "${APP_NAME}-backup-$(date +%Y%m%d-%H%M%S).yaml"
        echo -e "${GREEN}✅ 当前配置已备份${NC}"
    else
        echo -e "${YELLOW}⚠️  应用不存在，跳过备份${NC}"
    fi
}

# 部署应用
deploy_application() {
    echo -e "${GREEN}部署应用...${NC}"
    
    # 应用配置
    kubectl apply -f $DEPLOYMENT_FILE -n $NAMESPACE
    
    echo -e "${GREEN}✅ 部署配置已应用${NC}"
    
    # 等待部署完成
    echo "等待部署就绪..."
    kubectl rollout status deployment/$APP_NAME -n $NAMESPACE --timeout=600s
    
    echo -e "${GREEN}✅ 部署完成${NC}"
}

# 检查部署状态
check_deployment_status() {
    echo -e "${GREEN}检查部署状态...${NC}"
    
    # 获取Pod状态
    echo "Pod状态:"
    kubectl get pods -l app=$APP_NAME -n $NAMESPACE -o wide
    
    echo
    echo "Deployment状态:"
    kubectl get deployment $APP_NAME -n $NAMESPACE
    
    echo
    echo "Service状态:"
    kubectl get service ${APP_NAME}-service -n $NAMESPACE 2>/dev/null || echo "Service未找到"
    
    echo
    echo "HPA状态:"
    kubectl get hpa ${APP_NAME}-hpa -n $NAMESPACE 2>/dev/null || echo "HPA未找到"
}

# 监控资源使用
monitor_resources() {
    echo -e "${GREEN}开始监控资源使用情况 (${MONITOR_DURATION}秒)...${NC}"
    
    local end_time=$(($(date +%s) + MONITOR_DURATION))
    local count=0
    
    while [ $(date +%s) -lt $end_time ]; do
        count=$((count + 1))
        echo -e "\n${BLUE}=== 监控轮次 $count ($(date)) ===${NC}"
        
        # Pod资源使用
        echo "Pod资源使用情况:"
        kubectl top pods -l app=$APP_NAME -n $NAMESPACE 2>/dev/null || echo "无法获取Pod资源使用数据"
        
        # 节点资源使用
        echo -e "\n节点资源使用情况:"
        kubectl top nodes 2>/dev/null || echo "无法获取节点资源使用数据"
        
        # Pod分布
        echo -e "\nPod节点分布:"
        kubectl get pods -l app=$APP_NAME -n $NAMESPACE -o custom-columns="NAME:.metadata.name,NODE:.spec.nodeName,STATUS:.status.phase" --no-headers | sort -k2
        
        sleep 30
    done
}

# 生成报告
generate_report() {
    echo -e "${GREEN}生成部署报告...${NC}"
    
    local report_file="deployment-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Java应用资源优化部署报告"
        echo "=========================="
        echo "生成时间: $(date)"
        echo "应用名称: $APP_NAME"
        echo "命名空间: $NAMESPACE"
        echo
        
        echo "部署配置:"
        echo "--------"
        kubectl get deployment $APP_NAME -n $NAMESPACE -o yaml | grep -A 10 "resources:"
        echo
        
        echo "当前Pod状态:"
        echo "----------"
        kubectl get pods -l app=$APP_NAME -n $NAMESPACE -o wide
        echo
        
        echo "节点分布统计:"
        echo "----------"
        kubectl get pods -l app=$APP_NAME -n $NAMESPACE -o custom-columns="NODE:.spec.nodeName" --no-headers | sort | uniq -c
        echo
        
        echo "资源使用情况:"
        echo "----------"
        kubectl top pods -l app=$APP_NAME -n $NAMESPACE 2>/dev/null || echo "无法获取资源使用数据"
        echo
        
        echo "事件日志:"
        echo "-------"
        kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | grep $APP_NAME | tail -10
        
    } > $report_file
    
    echo -e "${GREEN}✅ 报告已生成: $report_file${NC}"
}

# 提供回滚选项
offer_rollback() {
    echo -e "${YELLOW}如需回滚到之前的配置，请执行:${NC}"
    echo "kubectl rollout undo deployment/$APP_NAME -n $NAMESPACE"
    echo
    echo -e "${YELLOW}查看回滚历史:${NC}"
    echo "kubectl rollout history deployment/$APP_NAME -n $NAMESPACE"
}

# 主函数
main() {
    echo -e "${BLUE}开始执行部署流程...${NC}"
    
    check_prerequisites
    echo
    
    backup_current_config
    echo
    
    deploy_application
    echo
    
    check_deployment_status
    echo
    
    # 询问是否进行监控
    read -p "是否开始资源监控? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        monitor_resources
        echo
    fi
    
    generate_report
    echo
    
    offer_rollback
    
    echo -e "${GREEN}✅ 部署流程完成！${NC}"
    echo
    echo -e "${BLUE}后续建议:${NC}"
    echo "1. 持续监控应用性能和资源使用情况"
    echo "2. 根据实际使用情况微调资源配置"
    echo "3. 定期运行 ./check-k8s-node-balance.sh 检查负载均衡"
    echo "4. 关注应用日志和监控指标"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
