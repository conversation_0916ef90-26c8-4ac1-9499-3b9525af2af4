Stack trace:
Frame         Function      Args
0007FFFFA270  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA270, 0007FFFF9170) msys-2.0.dll+0x1FE8E
0007FFFFA270  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA548) msys-2.0.dll+0x67F9
0007FFFFA270  000210046832 (000210286019, 0007FFFFA128, 0007FFFFA270, 000000000000) msys-2.0.dll+0x6832
0007FFFFA270  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA270  000210068E24 (0007FFFFA280, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA550  00021006A225 (0007FFFFA280, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE1AA10000 ntdll.dll
7FFE17F10000 KERNEL32.DLL
7FFE16AB0000 KERNELBASE.dll
7FFE18150000 USER32.dll
7FFE178F0000 win32u.dll
7FFE1A550000 GDI32.dll
7FFE17700000 gdi32full.dll
7FFE16F10000 msvcp_win.dll
7FFE16D50000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE1A920000 advapi32.dll
7FFE17C50000 msvcrt.dll
7FFE1A4B0000 sechost.dll
7FFE19D10000 RPCRT4.dll
7FFE16400000 CRYPTBASE.DLL
7FFE16E80000 bcryptPrimitives.dll
7FFE19FA0000 IMM32.DLL
