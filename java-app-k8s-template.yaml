# Java应用Kubernetes部署模板
# 针对64GB节点优化的资源配置

apiVersion: apps/v1
kind: Deployment
metadata:
  name: java-app
  labels:
    app: java-app
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: java-app
  template:
    metadata:
      labels:
        app: java-app
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/actuator/prometheus"
    spec:
      # 反亲和性配置 - 避免多个Pod调度到同一节点
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - java-app
              topologyKey: kubernetes.io/hostname
          # 软性反亲和 - 尽量分散到不同可用区
          - weight: 50
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - java-app
              topologyKey: topology.kubernetes.io/zone
      
      # 节点选择器 - 可选，用于指定特定类型的节点
      # nodeSelector:
      #   node-type: worker
      #   memory-size: 64gb
      
      containers:
      - name: java-app
        image: your-registry/java-app:latest
        imagePullPolicy: Always
        
        # 关键资源配置
        resources:
          requests:
            memory: "2Gi"      # 推荐配置：反映Java应用实际需求
            cpu: "500m"        # 0.5 CPU核心
            ephemeral-storage: "1Gi"
          limits:
            memory: "6Gi"      # 最大内存限制
            cpu: "2000m"       # 最大2个CPU核心
            ephemeral-storage: "5Gi"
        
        # Java应用环境变量配置
        env:
        - name: JAVA_OPTS
          value: >-
            -Xms2g
            -Xmx5g
            -XX:+UseG1GC
            -XX:MaxGCPauseMillis=200
            -XX:+PrintGCDetails
            -XX:+PrintGCTimeStamps
            -XX:+PrintGCApplicationStoppedTime
            -XX:+UseStringDeduplication
            -XX:+OptimizeStringConcat
            -Djava.security.egd=file:/dev/./urandom
            -Dspring.profiles.active=production
        
        # 应用配置
        - name: SERVER_PORT
          value: "8080"
        - name: MANAGEMENT_PORT
          value: "8081"
        - name: LOG_LEVEL
          value: "INFO"
        
        # 端口配置
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: management
          containerPort: 8081
          protocol: TCP
        
        # 健康检查配置
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: management
          initialDelaySeconds: 90    # Java应用启动较慢
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: management
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        # 启动探针 - 给Java应用更多启动时间
        startupProbe:
          httpGet:
            path: /actuator/health
            port: management
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 12       # 最多等待2分钟启动
          successThreshold: 1
        
        # 资源和安全配置
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          runAsGroup: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
        
        # 卷挂载
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
        - name: config
          mountPath: /app/config
          readOnly: true
      
      # 卷定义
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        emptyDir: {}
      - name: config
        configMap:
          name: java-app-config
      
      # Pod安全配置
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      
      # DNS配置
      dnsPolicy: ClusterFirst
      dnsConfig:
        options:
        - name: ndots
          value: "2"
        - name: edns0
      
      # 优雅关闭配置
      terminationGracePeriodSeconds: 60
      
      # 镜像拉取密钥
      # imagePullSecrets:
      # - name: registry-secret

---
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: java-app-service
  labels:
    app: java-app
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
  - name: management
    port: 8081
    targetPort: management
    protocol: TCP
  selector:
    app: java-app

---
# HPA配置 - 水平Pod自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: java-app-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: java-app
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
# PodDisruptionBudget - 确保服务可用性
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: java-app-pdb
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: java-app
