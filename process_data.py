#!/usr/bin/env python3
"""
Script to process the given data and extract unique identifiers,
then format them as email addresses with @h3c.com domain.
"""

def process_data():
    # The raw data from your input
    raw_data = """
    l12451
    l02107
    w05973
    s13629
    lkf7238
    z22968
    d22767
    d22767
    l12451
    z15077
    x18843
    s17054
    w05973
    c18873
    l18465
    z15077
    z10127
    x18843
    w05973
    l21034
    z04963
    z20337
    s13629
    y11335
    l20095
    mys4645
    z15077
    c29974
    x18843
    t25411
    m19756
    z15077
    s63931
    s63931
    z15077
    z15077
    h24002
    z17415
    w05973
    t26329
    c18873
    l31363
    c18873
    z10127
    h24002
    m19756
    m19756
    z02056
    l18465
    z15077
    s63931
    c20252
    l11384
    c17740
    z10127
    l26880
    h24002
    l18465
    w05973
    z20337
    y24000
    w16051
    xkf7490
    z10127
    s01447
    c20252
    z04963
    l11249
    w08006
    c63596
    w05973
    c20253
    l26880
    h24002
    mys4645
    w05973
    h24002
    t62525
    s26732
    d26631
    l22886
    l18465
    w05973
    sys3398
    y29249
    y11386
    c18873
    x03862
    z02056
    z22968
    c18873
    l26880
    z17415
    mys4645
    y24000
    s26732
    c17740
    t26329
    m19756
    s63931
    w22634
    z15077
    d26631
    l12328
    l13153
    l22595
    c18873
    l06699
    s63931
    c17740
    lys44938
    mys4645
    l11384
    t25411
    y29249
    h19727
    w05973
    w27025
    m19756
    k11092
    l11384
    c20253
    c17954
    m19756
    lkf7238
    c63981
    l26880
    l26880
    l12328
    z17554
    f23104
    l04249
    z30131
    c20252
    w05973
    """
    
    # Split the data into lines and clean up
    lines = [line.strip() for line in raw_data.strip().split('\n') if line.strip()]
    
    # Remove duplicates while preserving order
    unique_identifiers = []
    seen = set()
    
    for identifier in lines:
        if identifier not in seen:
            unique_identifiers.append(identifier)
            seen.add(identifier)
    
    # Format as email addresses
    emails = [f"{identifier}@h3c.com" for identifier in unique_identifiers]
    
    # Join with semicolons
    result = ";".join(emails)
    
    return result, unique_identifiers

if __name__ == "__main__":
    result, identifiers = process_data()
    
    print("Processed email addresses:")
    print(result)
    
    print(f"\nTotal unique identifiers found: {len(identifiers)}")
    print("\nFirst 10 identifiers:")
    for i, identifier in enumerate(identifiers[:10]):
        print(f"{i+1}. {identifier}")
