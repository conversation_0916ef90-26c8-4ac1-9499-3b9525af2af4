#!/bin/bash

# VPA (Vertical Pod Autoscaler) 安装和配置脚本
# 用于解决Java应用资源配置和节点负载均衡问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

VPA_VERSION="1.0.0"  # 可根据需要调整版本

echo -e "${BLUE}=== VPA (Vertical Pod Autoscaler) 安装配置脚本 ===${NC}"
echo "VPA版本: $VPA_VERSION"
echo "安装时间: $(date)"
echo

# 检查前置条件
check_prerequisites() {
    echo -e "${GREEN}1. 检查前置条件...${NC}"
    
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}错误: kubectl未安装${NC}"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        echo -e "${RED}错误: 无法连接到Kubernetes集群${NC}"
        exit 1
    fi
    
    # 检查Kubernetes版本
    k8s_version=$(kubectl version --short --client | grep -oE 'v[0-9]+\.[0-9]+')
    echo "Kubernetes版本: $k8s_version"
    
    # 检查metrics-server
    if ! kubectl get deployment metrics-server -n kube-system &> /dev/null; then
        echo -e "${YELLOW}⚠️  警告: metrics-server未安装，VPA需要metrics-server支持${NC}"
        echo "请先安装metrics-server: kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml"
    fi
    
    echo -e "${GREEN}✅ 前置条件检查完成${NC}"
}

# 下载VPA配置文件
download_vpa_manifests() {
    echo -e "${GREEN}2. 下载VPA配置文件...${NC}"
    
    # 创建临时目录
    mkdir -p vpa-manifests
    cd vpa-manifests
    
    # 下载VPA组件配置
    echo "下载VPA组件配置文件..."
    
    # VPA CRD定义
    cat > vpa-crd.yaml << 'EOF'
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: verticalpodautoscalers.autoscaling.k8s.io
spec:
  group: autoscaling.k8s.io
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              targetRef:
                type: object
              updatePolicy:
                type: object
              resourcePolicy:
                type: object
          status:
            type: object
  scope: Namespaced
  names:
    plural: verticalpodautoscalers
    singular: verticalpodautoscaler
    kind: VerticalPodAutoscaler
    shortNames:
    - vpa
EOF

    echo -e "${GREEN}✅ VPA配置文件准备完成${NC}"
}

# 安装VPA
install_vpa() {
    echo -e "${GREEN}3. 安装VPA组件...${NC}"
    
    # 方法1: 使用官方脚本安装
    echo "使用官方安装脚本..."
    
    # 克隆VPA仓库
    if [ ! -d "autoscaler" ]; then
        git clone https://github.com/kubernetes/autoscaler.git
    fi
    
    cd autoscaler/vertical-pod-autoscaler/
    
    # 执行安装脚本
    ./hack/vpa-install.sh
    
    cd ../../
    
    echo -e "${GREEN}✅ VPA安装完成${NC}"
}

# 验证VPA安装
verify_vpa_installation() {
    echo -e "${GREEN}4. 验证VPA安装...${NC}"
    
    # 检查VPA CRD
    echo "检查VPA CRD..."
    kubectl get crd verticalpodautoscalers.autoscaling.k8s.io
    
    # 检查VPA组件Pod
    echo -e "\n检查VPA组件Pod状态..."
    kubectl get pods -n kube-system | grep vpa
    
    # 检查VPA组件服务
    echo -e "\n检查VPA组件部署状态..."
    kubectl get deployment -n kube-system | grep vpa
    
    echo -e "${GREEN}✅ VPA验证完成${NC}"
}

# 创建Java应用的VPA配置
create_java_app_vpa() {
    echo -e "${GREEN}5. 创建Java应用VPA配置...${NC}"
    
    # 推荐模式VPA - 仅提供建议，不自动更新
    cat > java-app-vpa-recommend.yaml << 'EOF'
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: java-app-vpa-recommend
  namespace: default
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: java-app
  updatePolicy:
    updateMode: "Off"  # 仅推荐，不自动更新
  resourcePolicy:
    containerPolicies:
    - containerName: java-app
      maxAllowed:
        memory: "6Gi"
        cpu: "2000m"
      minAllowed:
        memory: "1Gi"
        cpu: "500m"
      controlledResources: ["cpu", "memory"]
EOF

    # 自动更新模式VPA - 自动调整资源并重启Pod
    cat > java-app-vpa-auto.yaml << 'EOF'
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: java-app-vpa-auto
  namespace: default
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: java-app
  updatePolicy:
    updateMode: "Auto"  # 自动更新资源配置
  resourcePolicy:
    containerPolicies:
    - containerName: java-app
      maxAllowed:
        memory: "6Gi"
        cpu: "2000m"
      minAllowed:
        memory: "1Gi"
        cpu: "500m"
      controlledResources: ["cpu", "memory"]
      controlledValues: "RequestsAndLimits"  # 同时调整requests和limits
EOF

    # 初始资源更新模式VPA - 仅在Pod创建时应用推荐值
    cat > java-app-vpa-initial.yaml << 'EOF'
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: java-app-vpa-initial
  namespace: default
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: java-app
  updatePolicy:
    updateMode: "Initial"  # 仅在Pod创建时应用
  resourcePolicy:
    containerPolicies:
    - containerName: java-app
      maxAllowed:
        memory: "6Gi"
        cpu: "2000m"
      minAllowed:
        memory: "1Gi"
        cpu: "500m"
      controlledResources: ["cpu", "memory"]
EOF

    echo -e "${GREEN}✅ VPA配置文件已创建${NC}"
    echo "配置文件说明:"
    echo "- java-app-vpa-recommend.yaml: 仅提供资源建议，不自动更新"
    echo "- java-app-vpa-auto.yaml: 自动调整资源并重启Pod"
    echo "- java-app-vpa-initial.yaml: 仅在Pod创建时应用推荐值"
}

# 创建VPA监控脚本
create_vpa_monitoring_script() {
    echo -e "${GREEN}6. 创建VPA监控脚本...${NC}"
    
    cat > monitor-vpa-recommendations.sh << 'EOF'
#!/bin/bash

# VPA推荐值监控脚本

echo "=== VPA推荐值监控 ==="
echo "监控时间: $(date)"
echo

# 获取所有VPA对象
echo "当前VPA配置:"
kubectl get vpa --all-namespaces

echo -e "\n=== VPA推荐详情 ==="

# 遍历所有VPA对象并显示推荐值
for vpa in $(kubectl get vpa -o name); do
    echo -e "\n--- $vpa ---"
    kubectl describe $vpa | grep -A 20 "Recommendation:"
done

echo -e "\n=== 对比当前Pod资源配置 ==="
kubectl get pods -l app=java-app -o custom-columns=\
"NAME:.metadata.name,\
MEMORY_REQUEST:.spec.containers[*].resources.requests.memory,\
MEMORY_LIMIT:.spec.containers[*].resources.limits.memory,\
CPU_REQUEST:.spec.containers[*].resources.requests.cpu,\
CPU_LIMIT:.spec.containers[*].resources.limits.cpu"

echo -e "\n=== Pod实际资源使用 ==="
kubectl top pods -l app=java-app
EOF

    chmod +x monitor-vpa-recommendations.sh
    
    echo -e "${GREEN}✅ VPA监控脚本已创建: monitor-vpa-recommendations.sh${NC}"
}

# 提供使用指南
provide_usage_guide() {
    echo -e "${GREEN}7. VPA使用指南${NC}"
    echo "=================================="
    echo
    echo -e "${YELLOW}针对您的Java应用负载均衡问题，建议使用步骤:${NC}"
    echo
    echo "步骤1: 先使用推荐模式观察VPA建议"
    echo "kubectl apply -f java-app-vpa-recommend.yaml"
    echo
    echo "步骤2: 运行监控脚本查看推荐值"
    echo "./monitor-vpa-recommendations.sh"
    echo
    echo "步骤3: 根据推荐值手动调整Deployment资源配置"
    echo "或者使用自动模式: kubectl apply -f java-app-vpa-auto.yaml"
    echo
    echo -e "${YELLOW}VPA模式说明:${NC}"
    echo "- Off: 仅提供推荐，不更新Pod (推荐用于生产环境)"
    echo "- Initial: 仅在Pod创建时应用推荐值"
    echo "- Auto: 自动更新资源并重启Pod (谨慎使用)"
    echo
    echo -e "${YELLOW}查看VPA状态命令:${NC}"
    echo "kubectl get vpa"
    echo "kubectl describe vpa java-app-vpa-recommend"
    echo
    echo -e "${YELLOW}删除VPA:${NC}"
    echo "kubectl delete vpa java-app-vpa-recommend"
}

# 主函数
main() {
    check_prerequisites
    echo
    
    download_vpa_manifests
    echo
    
    echo -e "${YELLOW}是否继续安装VPA? (y/n):${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        install_vpa
        echo
        
        verify_vpa_installation
        echo
    else
        echo -e "${YELLOW}跳过VPA安装${NC}"
    fi
    
    create_java_app_vpa
    echo
    
    create_vpa_monitoring_script
    echo
    
    provide_usage_guide
    
    echo -e "${GREEN}✅ VPA配置完成！${NC}"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF
