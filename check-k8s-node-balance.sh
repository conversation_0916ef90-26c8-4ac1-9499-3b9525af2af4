#!/bin/bash

# Kubernetes节点负载均衡检查脚本
# 用于分析节点间资源分布和负载情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Kubernetes节点负载均衡分析 ===${NC}"
echo "分析时间: $(date)"
echo

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}错误: kubectl命令未找到${NC}"
    exit 1
fi

# 检查集群连接
if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}错误: 无法连接到Kubernetes集群${NC}"
    exit 1
fi

echo -e "${GREEN}1. 节点基本信息${NC}"
echo "----------------------------------------"
kubectl get nodes -o wide
echo

echo -e "${GREEN}2. 节点资源使用情况 (按内存使用排序)${NC}"
echo "----------------------------------------"
kubectl top nodes --sort-by=memory 2>/dev/null || echo "注意: metrics-server可能未安装，无法获取实时资源使用数据"
echo

echo -e "${GREEN}3. 各节点Pod分布统计${NC}"
echo "----------------------------------------"
declare -A node_pod_count
declare -A node_java_pod_count

# 获取所有节点
nodes=$(kubectl get nodes -o jsonpath='{.items[*].metadata.name}')

for node in $nodes; do
    # 统计总Pod数
    total_pods=$(kubectl get pods --all-namespaces --field-selector spec.nodeName=$node --no-headers 2>/dev/null | wc -l)
    node_pod_count[$node]=$total_pods
    
    # 统计Java应用Pod数 (假设包含java关键字)
    java_pods=$(kubectl get pods --all-namespaces --field-selector spec.nodeName=$node -o jsonpath='{.items[*].metadata.name}' 2>/dev/null | tr ' ' '\n' | grep -i java | wc -l)
    node_java_pod_count[$node]=$java_pods
    
    echo "节点 $node: 总Pod数=$total_pods, Java应用Pod数=$java_pods"
done
echo

echo -e "${GREEN}4. 节点资源分配情况${NC}"
echo "----------------------------------------"
for node in $nodes; do
    echo -e "${YELLOW}节点: $node${NC}"
    kubectl describe node $node | grep -A 5 "Allocated resources:" | grep -E "(Resource|memory|cpu)"
    echo
done

echo -e "${GREEN}5. 内存Request配置分析${NC}"
echo "----------------------------------------"
echo "Pod名称 | 命名空间 | 节点 | Memory Request | Memory Limit"
echo "-------|---------|------|---------------|-------------"

kubectl get pods --all-namespaces -o custom-columns=\
"NAME:.metadata.name,NAMESPACE:.metadata.namespace,NODE:.spec.nodeName,\
MEMORY_REQUEST:.spec.containers[*].resources.requests.memory,\
MEMORY_LIMIT:.spec.containers[*].resources.limits.memory" \
--no-headers 2>/dev/null | grep -v "<none>" | while read line; do
    echo "$line" | awk '{printf "%-20s | %-12s | %-15s | %-13s | %-12s\n", $1, $2, $3, $4, $5}'
done
echo

echo -e "${GREEN}6. 负载均衡分析${NC}"
echo "----------------------------------------"

# 计算Pod分布的标准差
total_pods=0
node_count=0
for node in $nodes; do
    total_pods=$((total_pods + ${node_pod_count[$node]}))
    node_count=$((node_count + 1))
done

if [ $node_count -gt 0 ]; then
    avg_pods=$((total_pods / node_count))
    echo "平均每节点Pod数: $avg_pods"
    echo "节点总数: $node_count"
    echo "Pod总数: $total_pods"
    echo
    
    echo "节点负载偏差分析:"
    max_deviation=0
    for node in $nodes; do
        pod_count=${node_pod_count[$node]}
        deviation=$((pod_count - avg_pods))
        abs_deviation=${deviation#-}  # 绝对值
        
        if [ $abs_deviation -gt $max_deviation ]; then
            max_deviation=$abs_deviation
        fi
        
        if [ $deviation -gt 5 ]; then
            echo -e "  ${RED}$node: $pod_count pods (偏差: +$deviation) - 负载过高${NC}"
        elif [ $deviation -lt -5 ]; then
            echo -e "  ${YELLOW}$node: $pod_count pods (偏差: $deviation) - 负载较低${NC}"
        else
            echo -e "  ${GREEN}$node: $pod_count pods (偏差: $deviation) - 负载正常${NC}"
        fi
    done
    
    echo
    if [ $max_deviation -gt 10 ]; then
        echo -e "${RED}⚠️  检测到严重的负载不均衡问题！${NC}"
        echo "建议:"
        echo "1. 检查Pod的资源Request配置是否合理"
        echo "2. 考虑使用Pod反亲和性规则"
        echo "3. 调整应用的内存Request值以更好地反映实际需求"
    elif [ $max_deviation -gt 5 ]; then
        echo -e "${YELLOW}⚠️  检测到轻微的负载不均衡${NC}"
        echo "建议适当调整资源配置"
    else
        echo -e "${GREEN}✅ 节点负载分布相对均衡${NC}"
    fi
fi

echo
echo -e "${GREEN}7. 资源配置建议${NC}"
echo "----------------------------------------"
echo "基于当前分析，针对Java应用的建议配置:"
echo
echo "对于64GB内存节点的Java应用:"
echo "- 如果当前Request为500MB，建议调整为1.5-2GB"
echo "- 如果当前Request为1GB，建议调整为2GB"
echo "- Limit保持6GB不变"
echo
echo "示例配置:"
echo "resources:"
echo "  requests:"
echo "    memory: \"2Gi\""
echo "    cpu: \"500m\""
echo "  limits:"
echo "    memory: \"6Gi\""
echo "    cpu: \"2000m\""
echo
echo "Java JVM参数建议:"
echo "JAVA_OPTS=\"-Xms2g -Xmx5g -XX:+UseG1GC -XX:MaxGCPauseMillis=200\""

echo
echo -e "${BLUE}分析完成！${NC}"
echo "如需更详细的分析，请查看生成的 k8s-resource-scheduling-analysis.md 文档"
