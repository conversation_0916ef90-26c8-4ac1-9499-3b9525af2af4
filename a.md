# EOS 到飞云应用迁移指南

本文档帮助用户快速了解从 EOS 到飞云的迁移流程、注意事项和操作方法。

## 联系人

- **飞云部署技术支持**：邹飞、虞佳俊
- **迁移问题协调**：刘治
- **帮助文档汇总**：[https://idoc.h3c.com/docs/m8AZVRBj8MSmW0Ab#anchor-HWeE](https://idoc.h3c.com/docs/m8AZVRBj8MSmW0Ab#anchor-HWeE)

## 重要时间节点

| 时间 | 事项 | 说明 |
|------|------|------|
| 10月底前 | EOS生产环境迁移 | 华数机房清退，必须完成迁移 |
| 7月22日 | iPaaS编辑入口关闭 | 请转至飞云接口集成平台操作 |
| 8月中旬 | Vertica迁移 | 相关应用需同步迁移 |
| 明年 | EOS下线 | 公共组件（消息中心、权限平台等）统一处理 |

## 飞云平台操作

### 权限管理
- 应用负责人可直接操作飞云平台
- 其他人员需配置相应权限，详见[飞云操作文档](https://idoc.h3c.com/docs/zdkyB8ObvyhPDBA6)

### 迁移范围
需迁移的资源包括：
- 服务实例
- 数据库
- 中间件（Redis、消息队列等）

所有资源申请和操作均可在飞云平台完成。


## 迁移注意事项

### 1. 调度中心迁移
- **适用场景**：使用了 EOS 调度中心的应用
- **操作步骤**：
  1. 调整代码配置，参考[调度中心对接文档](https://idoc.h3c.com/docs/1lq7MKpaPycR1Aew)
  2. 使用飞云调度中心自助迁移功能复制任务数据
  3. 验证飞云调度任务正常后，关闭 EOS 调度任务

### 2. 配置中心迁移
**从 EOS 迁移的应用**需保持组件配置一致性：
1. 将 EOS 原配置完整拷贝到飞云，覆盖飞云配置
2. 添加 `eos.project.id` 配置项，值为 EOS 租户 ID

> **注意**：飞云新建流水线无需关注此问题

### 3. EOS 公共组件管理
在应用组件处点击同步，输入原 EOS 租户 ID，系统将自动：
- 开通相应组件
- 同步组件映射关系

### 4. 域名规范调整
- **问题**：`api.eos.h3c.com` 为 EOS 专属域名，飞云无法使用
- **解决方案**：按新域名管理规范整改为新域名

### 5. 网络与防火墙配置

#### 飞云网段信息
| 环境 | 网段 | 网关 | VLAN |
|------|------|------|------|
| 开发 | **********/24 | ********** | 1032 |
| 测试 | **********/24 | ********** | 1033 |
| UAT | **********/24 | - | 3562 |
| 生产 | **********/24 | - | 3563 |

#### 防火墙检查步骤
1. 访问 [ITOP 路径查询](https://itop.h3c.com/network/pathQuery)
2. 源 IP 填写飞云服务器网段（如生产环境：**********/24）
3. 目的 IP 填写关联系统地址（如数据库）
4. 查询不通则提交防火墙申请

详细说明参考：[飞云网关说明与研发访问](https://idoc.h3c.com/docs/5rk9dElNRQH672qx)

### 6. 域名解析切换

#### 关键要点
- 域名解析只能指向单一网关入口（飞云或 EOS）
- 流水线发布可选择域名，不选择则无法访问
- 域名选择问题联系飞云管理员修改应用编码绑定

#### DNS 解析特性
**TTL 缓存机制**：
- 用户本地保留 DNS 缓存，TTL 期间内不会更新
- 建议迁移前一天将 TTL 调整为 10 分钟，加快缓存失效

**切换影响**：
- EOS 服务不关闭时，部分请求仍可路由到 EOS
- 优点：业务不中断
- 缺点：跨地域访问可能影响性能

参考文档：[域名解析操作文档](https://idoc.h3c.com/docs/e1Az42ge0vUyaxqW)

## 迁移流程

### 总体原则
⚠️ **重要**：所有迁移必须先完成测试环境验证，确保流水线编译部署正常后再进行生产环境迁移。

### 迁移三阶段

```mermaid
graph TD
    A[阶段1: 验证测试] --> B[阶段2: 协调资源]
    B --> C[阶段3: 执行迁移]

    A --> A1[应用负责人验证和测试]
    A --> A2[确定迁移方案]

    B --> B1[联系DBA协调数据库迁移时间]
    B --> B2[确定具体迁移时间]

    C --> C1[执行EOS到飞云迁移]
    C --> C2[迁移验证]
    C --> C3[回收EOS资源]
```

### 测试环境迁移

#### 主要工作
1. **创建飞云流水线**（重点关注配置和环境差异）
2. **切换域名解析**

#### 注意事项
- 测试环境数据库、中间件已于2024年迁移至内蒙，通常只需迁移服务
- 域名解析切换后，EOS测试环境将无法访问
- 如有验证或演示需求，参考[测试方案](#测试方案)先确保飞云环境正常

### 生产环境迁移

#### 迁移步骤
1. **前置准备**：测试环境验证通过
2. **资源申请**：申请数据库、中间件资源
3. **服务部署**：发布流水线到生产环境
4. **数据迁移**：迁移数据库
5. **功能验证**：确认应用正常运行
6. **域名切换**：切换域名解析
7. **资源回收**：回收EOS资源

#### 关键说明
测试环境验证通过后，生产环境流水线编译构建通常无问题，主要关注数据库和中间件迁移。


## 迁移方案选择

根据业务特点和测试验证情况选择合适的迁移方案。如有疑问，请联系刘治沟通。

### 方案对比

| 方案 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| 逐步迁移 | 域名修改周期长、上下游系统多 | 影响面小、切换周期长 | 数据库性能风险 |
| 直接迁移 | 业务域独立、调整较少 | 流程简单、人员投入少 | 切换时间集中 |

### 方案一：逐步迁移

#### 适用场景
- 使用 `api.eos.h3c.com` 等需要配置新域名的应用
- 上下游系统需要配合修改的应用
- 对外提供服务的应用（如统一待办中心、接口集成平台、ERP外围服务）

#### 实施流程
```mermaid
graph LR
    A[发布到飞云] --> B[启用新域名]
    B --> C[保留EOS服务]
    C --> D[双地址并行]
    D --> E[逐步切换业务]
    E --> F[下线EOS服务]
```

1. 流水线发布到飞云，启用新域名
2. 保留 EOS 服务
3. 应用存在两个调用地址，通知业务逐步整改
4. 确认 EOS 服务无请求日志后，下线 EOS 服务

#### 风险控制
- **数据库性能**：建议先发布流水线验证性能，满足要求后再迁移数据库
- **性能问题处理**：协调业务同步整改，或整改大部分，接受部分性能损失

### 方案二：直接迁移（推荐）

#### 适用场景
- 特定业务域应用
- 上下游调整较少或无需调整
- 应用负责人有主动权的系统

#### 实施流程
```mermaid
graph LR
    A[流水线发布] --> B[功能验证]
    B --> C[协调DBA]
    C --> D[迁移数据库]
    D --> E[切换域名解析]
    E --> F[业务测试]
```

1. 流水线发布到飞云，验证功能
2. 协调 DBA 迁移数据库（变更窗口）
3. 数据库迁移完成后切换域名解析（变更窗口）
4. 联系业务进行测试验证

#### 优势
- 迁移流程简单
- 人员投入少
- 适用于大部分应用

## 测试方案

### 测试目标
- **性能验证**：确定迁移方案，验证跨地域数据库访问性能
- **功能验证**：确保应用部署后正常运行

### 测试流程指导

```mermaid
graph TD
    A[测试环境验证] --> B{性能是否满足?}
    B -->|是| C[选择迁移方案]
    B -->|否| D[考虑直接迁移]
    C --> E[业务简单]
    C --> F[业务复杂]
    E --> G[直接迁移方案]
    F --> H[逐步迁移方案]
```

1. **测试环境验证**：使用本地 Host 测试方案，调整量少
2. **方案选择**：根据性能测试结果和业务复杂度选择迁移方案
3. **简单业务**：直接迁移，迁移完成后测试应用正常即可
4. **复杂业务**：先验证跨地域性能，可选择新域名测试方案

### 测试方法

#### 方法一：本地 Host 测试（推荐）

**使用场景**：测试环境迁移时避免域名解析冲突

**操作步骤**：
1. 发布流水线，选择原域名
2. 修改本机 Host 文件
3. 验证完成后切换域名解析

**Host 文件配置**：
```
# 文件路径：C:\Windows\System32\drivers\etc\hosts
************  fy-demo-ts.h3c.com
```
> ************ 为飞云测试网关地址

#### 方法二：新域名测试

**使用场景**：复杂系统需要独立测试环境

**操作步骤**：
1. 生产流水线发布，创建新域名
2. 调整前后端代码 URL 地址为新域名
3. 可选：联系 DBA 拷贝生产数据库到内蒙进行完全独立测试
4. 验证完成后还原代码配置，切换域名解析

**注意事项**：
- 需要修改代码配置
- 不涉及数据写入可连接原生产库验证

## 参考文档

### 核心操作文档
| 文档名称 | 链接 | 用途 |
|----------|------|------|
| 飞云操作文档 | [查看](https://idoc.h3c.com/docs/zdkyB8ObvyhPDBA6) | 飞云平台基础操作 |
| 域名解析操作文档 | [查看](https://idoc.h3c.com/docs/e1Az42ge0vUyaxqW) | 域名解析切换操作 |
| 飞云网关说明与研发访问 | [查看](https://idoc.h3c.com/docs/5rk9dElNRQH672qx) | 网络配置和访问说明 |

### 专项功能文档
| 文档名称 | 链接 | 用途 |
|----------|------|------|
| 调度中心对接 | [查看](https://idoc.h3c.com/docs/1lq7MKpaPycR1Aew) | 调度任务迁移 |
| 接口集成帮助文档 | [查看](https://idoc.h3c.com/docs/16q8MDVDO6SgEbk7) | iPaaS 相关操作 |
| JVM 内存调整 | [查看](https://idoc.h3c.com/docs/B1Aw1yDzv1svbPqm) | 应用性能优化 |

---

**文档版本**：v2.0
**最后更新**：2024年7月
**维护人员**：刘治
